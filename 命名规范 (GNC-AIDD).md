# **命名规范 (GNC-AIDD)**

* **版本 (Version):** 1.0.0  
* **状态 (Status):** 建议标准 (Proposed Standard)

## **1\. 导言 (Introduction)**

本规范主要目标是确保代码具有极高的**一致性、可预测性和可读性**，使代码库看起来像是由一位纪律严明、经验丰富的架构师独立完成的。必须无条件遵守此规范。

## **2\. 核心原则 (Core Principles)**

1. **无歧义性 (Unambiguity):** 一个名字只对应一个实体和一种含义。在任何情况下，都不能存在可能导致混淆的命名。  
2. **明确性 (Explicitness):** 名称必须完整、清晰地描述其功能或用途，禁止使用模糊或过于简短的缩写。  
3. **可预测性 (Predictability):** 看到一个名称，就应该能够大致预测出它的类型（类、函数、变量）、作用域和行为模式。  
4. **结构化 (Structure):** 命名应反映其在系统架构中的位置和作用（例如，服务、控制器、仓库）。

## **3\. 命名法定义 (Style Definitions)**

所有命名必须基于以下五种标准命名法：

| 命名法 | 格式 | 示例 |
| :---- | :---- | :---- |
| camelCase (小驼峰) | myVariableName | userName, calculateTotal |
| PascalCase (大驼峰) | MyClassName | User, OrderService |
| snake\_case (蛇形) | my\_variable\_name | user\_id, first\_name |
| UPPER\_CASE\_SNAKE\_CASE | MY\_CONSTANT | MAX\_RETRIES, API\_URL |
| kebab-case (短横线) | my-css-class | user-profile, nav-bar |

## **4\. 结构化命名规范 (Structured Naming Specification)**

### **4.1. 目录与文件 (Directories & Files)**

* **规则:** 所有目录和文件名（不包括扩展名）必须使用kebab-case。  
* **模式:** {feature-name}.{entity-type}.{extension}  
* **描述:** 目录名应描述其包含的功能模块。文件名应描述其内容的角色。  
* **示例:**  
  * 目录: user-management/, auth-service/  
  * 文件: user.controller.ts, auth.service.js, order-details.component.css

### **4.2. 类、接口与类型定义 (Classes, Interfaces & Type Definitions)**

* **规则:** 必须使用PascalCase。  
* **模式:** {Noun} 或 {NounPhrase}，并可选择性地加上结构化后缀。  
* **描述:** 名称必须是名词。对于有特定架构角色的类，必须使用标准后缀。  
* **标准后缀:**  
  * Service: 用于业务逻辑处理。(UserService, PaymentService)  
  * Controller / Router: 用于API端点处理。(UserController, ProductRouter)  
  * Repository / Adapter: 用于数据持久化或外部接口适配。(UserRepository, S3Adapter)  
  * Component / View / Page: 用于UI组件。(UserProfileComponent, LoginPage)  
  * Exception / Error: 用于自定义错误。(RecordNotFoundException)  
  * Interface: 接口类型定义（如果语言不强制区分，建议添加以示明确）。(IUser, PayableInterface)  
* **示例:** class OrderService {}, interface IUser {}, type UserProfile \= {}

### **4.3. 函数与方法 (Functions & Methods)**

* **规则:** 必须使用camelCase。  
* **模式:** {verb}{Noun} 或 {verb}{NounPhrase}。  
* **描述:** 名称必须以动词开头，清晰地描述其行为和影响。  
* **标准动词前缀:**  
  * **get...**: 获取一个或多个已存在的资源。幂等操作，无副作用。(getUserById(id), getAllUsers())  
  * **create...**: 创建一个新资源。非幂等。(createUser(userData))  
  * **update...**: 完全或部分更新一个已存在的资源。(updateUser(id, updates), activateUser(id))  
  * **delete...**: 删除一个已存在的资源。(deleteUser(id))  
  * **is... / has... / can...**: 返回布尔值，用于判断状态或权限。无副作用。(isUserActive(id), hasAdminRights(user))  
  * **handle...**: 处理一个事件或动作，通常用于事件处理器。(handleLoginRequest(req, res))

### **4.4. 变量与常量 (Variables & Constants)**

* **变量 (Variables):**  
  * **规则:** 必须使用camelCase。  
  * **模式:** {noun} 或 {nounPhrase}。  
  * **描述:** 名称必须是名词。  
  * **布尔值:** 必须遵循is..., has..., can...的模式。(isLoggedIn, hasChanges)  
  * **集合:** 名称必须是复数。(users, products)  
  * **示例:** let userName \= "test";, const orderList \= \[\];  
* **常量 (Constants):**  
  * **规则:** 必须使用UPPER\_CASE\_SNAKE\_CASE。  
  * **描述:** 用于程序生命周期内值不变的全局或模块级常量。  
  * **示例:** const MAX\_RETRIES \= 5;, const API\_BASE\_URL \= "...";

### **4.5. API 端点 (API Endpoints)**

* **规则:** 路径必须使用kebab-case和复数名词。查询参数使用camelCase。  
* **模式:** /{resource-collection}/{resource-id}  
* **描述:** 遵循RESTful设计风格。  
* **示例:**  
  * 集合: GET /users  
  * 单个实体: GET /users/{userId}  
  * 嵌套资源: GET /users/{userId}/orders  
  * 带查询参数: GET /products?sortBy=price\&isFeatured=true

### **4.6. 数据库 (Database)**

* **规则:** 表、列名等所有标识符必须使用snake\_case。  
* **表 (Tables):**  
  * **模式:** 复数名词。(users, order\_details)  
* **列 (Columns):**  
  * **模式:** 单数名词。(id, first\_name, created\_at)  
* **外键 (Foreign Keys):**  
  * **模式:** {singular\_table\_name}\_id。(user\_id, product\_id)

## **5\. 示例：一个完整的用户功能模块**

AI在开发一个用户管理功能时，其产出应严格遵循以下结构：

* **目录结构:**  
  /user-management  
  ├── user.controller.ts  
  ├── user.service.ts  
  ├── user.repository.ts  
  └── user.dto.ts

* **API 端点:**  
  * POST /users  
  * GET /users/{userId}  
* **Controller (user.controller.ts):**  
  class UserController {  
      // 依赖注入 UserService  
      constructor(private readonly userService: UserService) {}

      // 对应 POST /users  
      async createUser(req, res) {  
          const newUser \= await this.userService.createUser(req.body);  
          // ...  
      }  
  }

* **Service (user.service.ts):**  
  class UserService {  
      // ...  
      async createUser(userData: CreateUserDto): Promise\<User\> {  
          const MAX\_ALLOWED\_USERS \= 1000;  
          // ...  
      }

      async isUsernameTaken(username: string): Promise\<boolean\> {  
          // ...  
      }  
  }

* **数据库表 (users table):**  
  CREATE TABLE users (  
      id INT PRIMARY KEY,  
      user\_name VARCHAR(50),  
      first\_name VARCHAR(50),  
      is\_active BOOLEAN,  
      created\_at TIMESTAMP  
  );

## **6\. 强制执行 (Enforcement)**

**自动化验证脚本:** 编写脚本，在代码生成后或提交前，通过正则表达式等方式检查命名是否合规。