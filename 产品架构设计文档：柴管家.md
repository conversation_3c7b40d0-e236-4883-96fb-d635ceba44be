# **产品架构设计文档：柴管家**

版本： 1.4  
日期： 2025年7月29日

## **1\. 项目概览**

### **1.1 产品愿景**

为知识类、教培类个人IP运营者提供一站式私域运营解决方案，通过聚合多平台消息和AI能力，实现降本增效、激活社群、深化用户关系的目标。

### **1.2 核心问题与目标用户**

* **要解决的核心问题：**  
  1. **消息分散，效率低下：** 运营者需在微信、抖音、小红书等多个平台间频繁切换，回复大量重复性咨询，易遗漏商机。  
  2. **社群活跃度维护难：** 运营者精力有限，难以持续在多个社群制造话题、输出价值，导致社群沉寂。  
  3. **用户关系维护难：** 无法形成统一用户视图，难以进行精细化运营和深度关系维护。  
* **目标用户画像：**  
  * **知识IP主理人：** 如职场成长博主“思思”，痛点在于从重复的售前咨询中解放精力，并维持付费社群的活跃度。  
  * **教培机构IP运营：** 如考研机构运营“小张”，痛点在于从海量引流用户中筛选高意向线索，并进行规模化的社群培育与转化。

## **2\. 需求分析**

### **2.1 功能性需求 (MVP范围)**

* **模块一：渠道与消息管理**  
  * 功能点1.1：支持多平台（微信、抖音、小红书等）及同平台多账号的接入与别名管理。  
  * 功能点1.2：在统一工作台聚合所有已接入渠道的消息，并能通过原渠道进行回复。  
* **模块二：AI智能助理**  
  * 功能点2.1：提供基础的知识库（FAQ）管理功能。  
  * 功能点2.2：**AI副驾**：根据对话上下文，实时分析用户意图并生成回复建议供运营者选用。  
  * 功能点2.3：**AI托管**：对会话开启自动回复模式，基于置信度判断，高置信度问题自动发送回复，低置信度则暂停并提醒人工接管。  
* **第三方服务集成：**  
  * **消息渠道：** 微信、抖音、小红书、知识星球等。  
  * **AI模型：** 可插拔式集成第三方大语言模型（LLM）API。

### **2.2 非功能性需求**

| 质量属性 | 指标要求 |
| :---- | :---- |
| **可扩展性** | **部署模式：** 初期支持私有化部署，架构需具备向多租户SaaS平台平滑演进的能力。\<br\>**用户规模：** 初期支持1,000 IP主理人，一年内可支撑至5,000+ IP。每个IP实例需能管理10个以上的百人社群。 |
| **性能** | **消息同步延迟：** 外部平台消息到工作台的延迟 \< 5秒。\<br\>**AI响应时间：** AI意图分析与回复建议生成时间 \< 2秒。\<br\>**并发处理：** 需能承受单IP由营销活动等引发的上千条私信并发冲击。 |
| **可用性** | **未来SaaS服务：** 核心服务可用性 \> 99.5%。\<br\>**私有化部署：** 应用需具备断线自动重连机制，保障连接稳定性。 |
| **安全性** | 用户密码加盐哈希存储；第三方平台授权凭证（Token/Cookie）及聊天记录等敏感数据需加密存储；所有数据传输使用TLS 1.2+加密。 |

## **3\. 约束条件**

* **团队背景：** 后端团队熟悉 **Python** 技术栈，前端团队熟悉 **React** 技术栈。  
* **预算限制：** 尽可能降低基础设施和软件成本，优先选用**成熟的开源方案**。第三方大模型API调用成本由终端用户自行承担。  
* **时间限制：** **无明确上线日期**，允许优先保证架构的稳健性与可扩展性。

## **4\. 架构设计**

### **4.1 选定架构风格与理由**

**选定架构：** **模块化单体 \+ 事件驱动连接器 (Modular Monolith with Event-Driven Connectors)**

**设计理由：**

该架构是综合考量当前团队技能、预算约束、部署模式演进及未来扩展性后的最佳选择。

1. **模块化单体**：使用团队熟悉的Python构建一个内部逻辑清晰的单体应用，非常适合项目初期快速开发和迭代。更重要的是，它极大简化了初期的“私有化部署”需求，用户只需部署一个核心应用包即可。清晰的模块划分（如用户、消息、AI、知识库）为未来SaaS化时将模块平滑拆分为微服务奠定了坚实基础。  
2. **事件驱动连接器**：将与第三方平台（微信、抖音等）的对接逻辑剥离成独立的“连接器”进程/容器。连接器与核心后端通过消息队列进行异步通信。这样做带来了巨大优势：**高内聚低耦合**，增删修改任一平台的对接逻辑均不影响核心应用；**高弹性与隔离**，单个连接器的故障或平台接口变更不会拖垮整个系统；**易扩展**，未来增加新平台只需开发新的连接器即可。

我们当前阶段的核心权衡是 **“初期开发部署的简易度与未来架构的演进能力 \> 极致的分布式复杂性”**。

### **4.2 高阶架构图与数据流**

#### **高阶架构图**

```mermaid
graph TD
    subgraph ClientSide
        A["Web前端 (React SPA)"]
    end

    subgraph ServerSide
        B["核心后端服务 (Python/FastAPI)"]
        C["消息队列 (RabbitMQ)"]
        D["主数据库 (PostgreSQL)"]
        E["向量数据库 (ChromaDB)"]

        subgraph Connectors
            F1["微信连接器"]
            F2["抖音连接器"]
            F3["..."]
        end
    end

    subgraph ThirdParty
        G1["微信"]
        G2["抖音"]
        G3["..."]
    end

    A -- "HTTPS/REST API & WebSocket" --> B
    B -- "读写" --> D
    B -- "读写/索引" --> E
    B -- "生产/消费" --> C

    F1 <--> G1
    F2 <--> G2
    F3 <--> G3

    F1 -- "生产/消费" --> C
    F2 -- "生产/消费" --> C
    F3 -- "生产/消费" --> C
```

#### **核心数据流**

**1\. 接收消息**

```mermaid
sequenceDiagram
    participant P as "第三方平台 (如:微信)"
    participant C as "连接器 (Connector)"
    participant MQ as "消息队列 (RabbitMQ)"
    participant BE as "核心后端 (Backend)"
    participant FE as "Web前端 (Frontend)"

    P->>C: 发送新消息
    C->>MQ: 将消息推送到队列
    BE->>MQ: 订阅并消费消息
    BE->>BE: 处理消息 (AI分析等)
    BE-->>FE: 通过WebSocket推送消息
```

**2\. 发送消息**

```mermaid
sequenceDiagram
    participant FE as "Web前端 (Frontend)"
    participant BE as "核心后端 (Backend)"
    participant MQ as "消息队列 (RabbitMQ)"
    participant C as "连接器 (Connector)"
    participant P as "第三方平台 (如:微信)"

    FE->>BE: 用户点击发送 (HTTPS/REST)
    BE->>BE: 处理发送请求
    BE->>MQ: 将发送指令推送到队列
    C->>MQ: 订阅并消费指令
    C->>P: 调用平台API发送消息
```

### **4.3 核心组件说明**

* **Web前端：** 采用**React**构建的单页应用(SPA)，负责所有用户交互和实时消息展示。  
* **核心后端服务：** 基于**Python (推荐FastAPI)** 构建的模块化单体应用，通过RESTful API提供业务功能，通过WebSocket实现消息实时推送。  
* **外部连接器 (Connectors)：** 独立的**Python**进程，使用各平台非官方API或SDK，负责消息的收发适配。  
* **消息队列 (Message Broker)：** 采用**RabbitMQ**，确保连接器与后端核心之间的消息传递是异步、可靠且可扩展的。  
* **主数据库：** 采用**PostgreSQL**，存储用户、团队、知识库元数据、消息记录等结构化数据。  
* **向量数据库：** 采用**ChromaDB**，用于存储知识库内容的向量索引，实现高效的语义搜索，为AI回复建议提供支持。

## **5\. 技术栈选型**

| 领域 | 技术/框架 | 选型理由 |
| :---- | :---- | :---- |
| **后端** | Python \+ FastAPI | 团队熟悉Python；FastAPI提供高性能的异步IO能力，完美契合实时消息处理场景，文档友好，开发效率高。 |
| **前端** | React \+ Vite | 团队熟悉React；Vite提供极佳的开发体验和构建性能。 |
| **主数据库** | PostgreSQL | 功能强大，支持复杂查询和JSON，社区活跃，完全开源，是Python应用的首选关系型数据库。 |
| **向量数据库** | ChromaDB | Python原生，轻量且易于私有化部署（可嵌入应用），满足MVP阶段的AI语义搜索需求，开源免费。 |
| **消息队列** | RabbitMQ | 成熟可靠的企业级消息队列，提供多种消息模式，能有力支撑事件驱动架构，确保消息不丢失。 |
| **部署** | Docker \+ Docker Compose | 实现环境一致性，将后端、连接器、数据库等所有组件打包，极大简化私有化部署的复杂度，实现“一键启动”。 |

## **6\. 后续步骤**

1. **接口与协议设计：** 优先定义核心后端与外部连接器之间通过消息队列传递的**消息数据结构（Schema）**，以及前后端之间的**REST API**和**WebSocket**通信协议。  
2. **数据库表结构设计：** 进行详细的PostgreSQL数据库表结构设计，尤其要为未来的多租户和团队权限（RBAC）模型预留好字段。  
3. **原型验证：** 优先开发技术风险最高的**微信连接器**，验证与微信PC端或网页端通信的稳定性与可行性。  
4. **CI/CD搭建：** 建立持续集成/持续部署流水线，自动化代码测试和Docker镜像的构建，提升开发效率和质量。